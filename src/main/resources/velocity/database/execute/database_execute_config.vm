#set($page_title='页面标题')

#parse("database/execute/database_execute_result.vm")

<style>
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item label="ID">
            <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
        </el-form-item>
        <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
        <el-form-item>
            <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
            <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column label="" type="expand">
            <template slot-scope="props">
                <el-form label-position="left" inline class="table-expand">
                    <el-form-item label="创建时间">
                        <span>{{ props.row.createTime }}</span>
                    </el-form-item>
                    <el-form-item label="更新时间">
                        <span>{{ props.row.updateTime }}</span>
                    </el-form-item>
                    <el-form-item label="创建用户ID">
                        <span>{{ props.row.createUserId }}</span>
                    </el-form-item>
                    <el-form-item label="更新用户ID">
                        <span>{{ props.row.updateUserId }}</span>
                    </el-form-item>
                </el-form>
            </template>
        </el-table-column>
        <el-table-column prop="id" label="ID"></el-table-column>
        <el-table-column prop="name" label="名称"></el-table-column>
        <el-table-column prop="databaseId" label="数据库ID"></el-table-column>
        <el-table-column prop="databaseName" label="数据库名称"></el-table-column>
        <el-table-column prop="status" label="状态"></el-table-column>
        <el-table-column prop="sql" label="SQL语句"></el-table-column>
        <el-table-column prop="keyStart" label="起始键值"></el-table-column>
        <el-table-column prop="keyEnd" label="结束键值"></el-table-column>
        <el-table-column prop="batchOffset" label="批次偏移量"></el-table-column>
        <el-table-column prop="currentKeyValue" label="当前键值"></el-table-column>
        <el-table-column prop="lastExecuteTime" label="最后执行时间"></el-table-column>
        <el-table-column label="操作">
            <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                <el-button type="info" size="small" @click="showResultDialog(scope.row.id)">结果</el-button>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

    <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
        <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
            <el-form-item label="名称" prop="name">
                <el-input v-model="addEditForm.name" placeholder="执行任务名称"></el-input>
            </el-form-item>
            <el-form-item label="数据库ID" prop="databaseId">
                <el-select v-model="addEditForm.databaseId" placeholder="请选择数据库">
                    <el-option v-for="item in databases" :key="item.databaseId" :label="item.name" :value="item.databaseId"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="数据库名称" prop="databaseName">
                <el-input v-model="addEditForm.databaseName" placeholder="数据库名,允许为空"></el-input>
            </el-form-item>
            <el-form-item label="SQL语句" prop="sql">
                <el-input type="textarea" :rows="5" v-model="addEditForm.sql" placeholder="执行的SQL，该sql中应该包含:keyStart 和 :keyEnd 两个变量占位符，用于每一批次执行时确定范围"></el-input>
            </el-form-item>
            <el-form-item label="起始键值" prop="keyStart">
                <el-input v-model="addEditForm.keyStart" placeholder="开始的主键，包含；当它是数字时，程序会自动按数字处理；同时也支持非数字类型的key"></el-input>
            </el-form-item>
            <el-form-item label="结束键值" prop="keyEnd">
                <el-input v-model="addEditForm.keyEnd" placeholder="结束的主键，包含；当它是数字时，程序会自动按数字处理；同时也支持非数字类型的key"></el-input>
            </el-form-item>
            <el-form-item label="批次偏移量" prop="batchOffset">
                <el-input v-model="addEditForm.batchOffset" placeholder="每次执行的偏移量，即每次执行范围=[当前key,当前key+batch_offset)"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="doAddOrEdit">确定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="执行结果" :visible.sync="showResult" width="90%" top="5vh" :close-on-click-modal="false">
        <database-execute-result v-if="showResult" :task-id="currentTaskId" />
        <span slot="footer">
            <el-button @click="showResult = false">关闭</el-button>
        </span>
    </el-dialog>

</div>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    var vm = new Vue({
        el: '#app',
        data: {
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
            total: 0, tableData: [], tableLoading: false,
            showDialog: false, dialogTitle: '',
            databases: [],
            showResult: false, // 控制执行结果弹窗
            currentTaskId: null // 当前查看的任务ID
        },
        created: function() {
            this.getData()
            var that = this
            Resource.get("${_contextPath_}/database/get_database_for_select", {}, function(resp){
                that.databases = resp.data
            })
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/database_execute_config/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/database_execute_config/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增数据库执行任务配置表' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/database_execute_config/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            },
            showResultDialog: function(taskId) {
                this.currentTaskId = taskId
                this.showResult = true
            }
        }
    })
</script>
