package com.pugwoo.branch.chart.dto.highchart;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.chart.dto.valuedto.DateSingleDimValueDTO;
import com.pugwoo.branch.chart.utils.ChartCommonUtils;
import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CreatePercentageAreaDTO {

    @NotNull
    private List<DateSingleDimValueDTO> data = new ArrayList<>();
    @NotNull
    private String title = "";

    public static CreatePercentageAreaDTO from(CreateSandboxReq req) {
        ChartCommonUtils.handleDataAsFirstColumnXAxis(req);

        CreatePercentageAreaDTO createPercentageAreaDTO = new CreatePercentageAreaDTO();
        Integer columns = ChartCommonUtils.determinateColumn(req.getData());
        if (columns != null) {
            List<DateSingleDimValueDTO> data = new ArrayList<>();
            if (columns == 2) { // 1. 处理只有日期+值的表头的场景
                for (List<String> row : req.getData()) {
                    DateSingleDimValueDTO d = new DateSingleDimValueDTO();
                    d.setDate(row.getFirst());
                    d.setDim("默认");
                    d.setValue(NumberUtils.parseBigDecimal(row.get(1)));
                    data.add(d);
                }
            } else if (columns == 3) { // 2. 处理有日期+dim+值的场景
                for (List<String> row : req.getData()) {
                    DateSingleDimValueDTO d =new DateSingleDimValueDTO();
                    d.setDate(row.getFirst());
                    d.setDim(row.get(1));
                    d.setValue(NumberUtils.parseBigDecimal(row.get(2)));
                    data.add(d);
                }
            } else {
                throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "表头列数只支持2或3列，实际列数：" + req.getTitles().size());
            }
            createPercentageAreaDTO.setData(data);
        }

        createPercentageAreaDTO.setTitle(req.getName() == null ? "" : req.getName());
        return createPercentageAreaDTO;
    }

}
