package com.pugwoo.branch.chart.dto.echarts;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.chart.dto.valuedto.DateSingleDimValueDTO;
import com.pugwoo.branch.chart.utils.ChartCommonUtils;
import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 创建多折线图
 */
@Slf4j
@Data
public class CreateLineDTO {

    @Data
    public static class YAxis {
        /**Y轴名称*/
        private String name;
        /**该y轴对应的线条*/
        private List<String> lines;
    }

    @NotNull
    private List<DateSingleDimValueDTO> data = new ArrayList<>();
    @NotNull
    private String title = "";
    @NotNull
    private Boolean smooth = false;
    /**约定key为*，表示匹配所有的线条*/
    @NotNull
    private Map<String, String> colors = new HashMap<>();
    /**约定key为*，表示匹配所有的线条*/
    @NotNull
    private Map<String, String> styles = new HashMap<>();
    /**是否显示缩略图*/
    @NotNull
    private Boolean showZoom = false;
    /**是否把null值连起来*/
    @NotNull
    private Boolean connectNulls = false;

    /**x轴名称*/
    private String xAxisName = "";
    /**y轴名称，当yAxis为空时生效*/
    private String yAxisName = "";

    /**适合于多个y轴的场景*/
    private List<YAxis> yAxis = new ArrayList<>();

    public static CreateLineDTO from(CreateSandboxReq req) {
        ChartCommonUtils.handleDataAsFirstColumnXAxis(req);

        CreateLineDTO createLineDTO = new CreateLineDTO();

        // 1. 处理数据
        Integer columns = ChartCommonUtils.determinateColumn(req.getData());
        if (columns != null) {
            List<DateSingleDimValueDTO> data = new ArrayList<>();
            if (columns == 2) { // 1. 处理只有日期+值的表头的场景
                for (List<String> row : req.getData()) {
                    DateSingleDimValueDTO d = new DateSingleDimValueDTO();
                    d.setDate(row.getFirst());
                    d.setDim("默认");
                    d.setValue(NumberUtils.parseBigDecimal(row.get(1)));
                    data.add(d);
                }
            } else if (columns == 3) { // 2. 处理有日期+dim+值的场景
                for (List<String> row : req.getData()) {
                    DateSingleDimValueDTO d =new DateSingleDimValueDTO();
                    d.setDate(row.getFirst());
                    d.setDim(row.get(1));
                    d.setValue(NumberUtils.parseBigDecimal(row.get(2)));
                    data.add(d);
                }
            } else {
                throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "表头列数只支持2或3列，实际列数：" + req.getTitles().size());
            }
            createLineDTO.setData(data);
        }

        // 2. 处理x轴和y轴名称
        List<String> titles = req.getTitles();
        if (titles != null && titles.size() == 1) {
            createLineDTO.setXAxisName(titles.getFirst());
        } else if (titles != null && titles.size() >= 2) {
            createLineDTO.setXAxisName(titles.getFirst());
            createLineDTO.setYAxisName(titles.getLast());
        }

        // 3. 处理颜色和线条、其它配置
        Map<String, String> colors = ChartCommonUtils.getConfig(req.getConfigs(), "colors", createLineDTO.getColors(),
                o -> ChartCommonUtils.toMap("*", o),
                o -> MapUtils.transform(o, Object::toString));
        createLineDTO.setColors(colors);

        Map<String, String> styles = ChartCommonUtils.getConfig(req.getConfigs(), "styles", createLineDTO.getStyles(),
                o -> ChartCommonUtils.toMap("*", o),
                o -> MapUtils.transform(o, Object::toString));
        createLineDTO.setStyles(styles);

        List<YAxis> yAxis = ChartCommonUtils.getConfig(req.getConfigs(), "yAxis", createLineDTO.getYAxis(),
                null, null, o -> {
                    List<YAxis> yAxisList = new ArrayList<>();
                    for (Object obj : o) {
                        if (!(obj instanceof Map)) {
                            log.error("yAxis config must be map");
                            continue;
                        }
                        YAxis y = new YAxis();
                        Map<String, Object> map = (Map<String, Object>) obj;
                        y.setName(toString(map.get("name")));
                        Object lines = map.get("lines");
                        if (lines instanceof List) {
                            y.setLines(ListUtils.transform((List<?>) lines, Object::toString));
                        } else {
                            y.setLines(new ArrayList<>());
                        }
                        yAxisList.add(y);
                    }
                    return yAxisList;
        });
        createLineDTO.setYAxis(yAxis);

        createLineDTO.setSmooth(ChartCommonUtils.getConfig(req.getConfigs(), "smooth", createLineDTO.getSmooth(),
                ChartCommonUtils::parseBoolean));
        createLineDTO.setShowZoom(ChartCommonUtils.getConfig(req.getConfigs(), "showZoom", createLineDTO.getShowZoom(),
                ChartCommonUtils::parseBoolean));
        createLineDTO.setConnectNulls(ChartCommonUtils.getConfig(req.getConfigs(), "connectNulls", createLineDTO.getConnectNulls(),
                ChartCommonUtils::parseBoolean));

        createLineDTO.setTitle(req.getName() == null ? "" : req.getName());
        return createLineDTO;
    }

    private static String toString(Object obj) {
        if (obj == null) {
            return "";
        }
        return obj.toString();
    }

}
