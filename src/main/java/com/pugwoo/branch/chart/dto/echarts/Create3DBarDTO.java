package com.pugwoo.branch.chart.dto.echarts;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.chart.dto.valuedto.DateSingleDimValueDTO;
import com.pugwoo.branch.chart.utils.ChartCommonUtils;
import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 创建echarts 3d所需的数据和配置
 */
@Data
public class Create3DBarDTO {

    @NotNull
    private List<DateSingleDimValueDTO> data = new ArrayList<>();
    @NotNull
    private String title = "";

    /**场景宽度*/
    @NotNull
    private Integer boxWidth = 200;
    /**场景深度*/
    @NotNull
    private Integer boxDepth = 400;
    /**柱子尺寸*/
    @NotNull
    private BigDecimal barSize = BigDecimal.ONE;
    /**视角距离*/
    @NotNull
    private Integer distance = 150;
    /**最大视角距离*/
    @NotNull
    private Integer maxDistance = 400;
    /**高度的角度*/
    @NotNull
    private Integer heightAngle = 15;

    /**[可选]可以由用户指定max值，它会影响图形的渐变着色*/
    private BigDecimal maxValue;

    public static Create3DBarDTO from(CreateSandboxReq req) {
        ChartCommonUtils.handleDataAsFirstColumnXAxis(req);

        Create3DBarDTO create3DBarDTO = new Create3DBarDTO();
        create3DBarDTO.setTitle(req.getName() == null ? "" : req.getName());

        Integer columns = ChartCommonUtils.determinateColumn(req.getData());
        if (columns != null) {
            List<DateSingleDimValueDTO> data = new ArrayList<>();
            if (columns == 2) { // 1. 处理只有日期+值的表头的场景
                Map<String, Integer> rankMap = new HashMap<>();
                for (List<String> row : req.getData()) {
                    DateSingleDimValueDTO d = new DateSingleDimValueDTO();
                    d.setDate(row.getFirst());
                    d.setDim(String.valueOf(ChartCommonUtils.getRank(rankMap, row.getFirst())));
                    d.setValue(NumberUtils.parseBigDecimal(row.get(1)));
                    data.add(d);
                }
            } else if (columns == 3) { // 2. 处理有日期+dim+值的场景
                for (List<String> row : req.getData()) {
                    DateSingleDimValueDTO d =new DateSingleDimValueDTO();
                    d.setDate(row.getFirst());
                    d.setDim(row.get(1));
                    d.setValue(NumberUtils.parseBigDecimal(row.get(2)));
                    data.add(d);
                }
            } else {
                throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "表头列数只支持2或3列，实际列数：" + req.getTitles().size());
            }
            create3DBarDTO.setData(data);
        }

        // 3. 处理configs
        create3DBarDTO.setBoxWidth(ChartCommonUtils.getConfig(req.getConfigs(), "boxWidth", create3DBarDTO.getBoxWidth(),
                NumberUtils::parseInt, null));
        create3DBarDTO.setBoxDepth(ChartCommonUtils.getConfig(req.getConfigs(), "boxDepth", create3DBarDTO.getBoxDepth(),
                NumberUtils::parseInt, null));
        create3DBarDTO.setBarSize(ChartCommonUtils.getConfig(req.getConfigs(), "barSize", create3DBarDTO.getBarSize(),
                NumberUtils::parseBigDecimal, null));
        create3DBarDTO.setDistance(ChartCommonUtils.getConfig(req.getConfigs(), "distance", create3DBarDTO.getDistance(),
                NumberUtils::parseInt, null));
        create3DBarDTO.setHeightAngle(ChartCommonUtils.getConfig(req.getConfigs(), "heightAngle", create3DBarDTO.getHeightAngle(),
                NumberUtils::parseInt, null));
        create3DBarDTO.setMaxValue(ChartCommonUtils.getConfig(req.getConfigs(), "maxValue", create3DBarDTO.getMaxValue(),
                NumberUtils::parseBigDecimal, null));

        return create3DBarDTO;
    }

}
